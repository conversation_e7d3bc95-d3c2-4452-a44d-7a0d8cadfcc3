'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';

export default function Navbar() {
  const [isShopsOpen, setIsShopsOpen] = useState(false);
  const [isInformationsOpen, setIsInformationsOpen] = useState(false);
  const [isEmbedsOpen, setIsEmbedsOpen] = useState(false);

  return (
    <nav className="w-full px-6 py-3 bg-[#2c2c54]/95 backdrop-blur-md border-b border-white/10">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-3 group">
          <div className="relative">
            <div className="w-10 h-10 bg-gradient-to-br from-[#aab2d0] to-[#9b99b5] rounded-lg flex items-center justify-center">
              <Image
                src="/window.svg"
                alt="stun"
                width={24}
                height={24}
                className="filter invert"
              />
            </div>
          </div>
          <span className="text-xl font-bold text-white">
            stun
          </span>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center gap-1">
          <Link href="/commands" className="px-4 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-all text-sm font-medium">
            Commands
          </Link>
          <Link href="/status" className="px-4 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-all text-sm font-medium">
            Status
          </Link>
          <Link href="/avatars" className="px-4 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-all text-sm font-medium">
            Avatarhistory
          </Link>

          {/* Dropdown-style button */}
          <div className="relative group">
            <button className="flex items-center gap-1 px-4 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-all text-sm font-medium">
              Shops
              <ChevronDown size={16} />
            </button>
          </div>

          <div className="relative group">
            <button className="flex items-center gap-1 px-4 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-all text-sm font-medium">
              Informations
              <ChevronDown size={16} />
            </button>
          </div>

          <div className="relative group">
            <button className="flex items-center gap-1 px-4 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-all text-sm font-medium">
              Embeds
              <ChevronDown size={16} />
            </button>
          </div>
        </div>

        {/* Right Side */}
        <div className="hidden md:flex items-center gap-4">
          {/* Status Indicator */}
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-white/60 text-sm">1</span>
          </div>

          {/* Login Button */}
          <Link href="https://discord.gg/heistbot" className="bg-[#5865f2] hover:bg-[#4752c4] text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
            </svg>
            Login
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-white/70 hover:text-white transition-colors"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Menu */}
      <div className={`md:hidden mt-4 transition-all duration-300 ${
        mobileMenuOpen ? 'opacity-100 max-h-96' : 'opacity-0 max-h-0 overflow-hidden'
      }`}>
        <div className="flex flex-col gap-2 pt-4 border-t border-white/10">
          <Link href="/commands" className="text-white/70 hover:text-white transition-colors py-2 text-sm font-medium">
            Commands
          </Link>
          <Link href="/status" className="text-white/70 hover:text-white transition-colors py-2 text-sm font-medium">
            Status
          </Link>
          <Link href="/avatars" className="text-white/70 hover:text-white transition-colors py-2 text-sm font-medium">
            Avatarhistory
          </Link>
          <button className="text-white/70 hover:text-white transition-colors py-2 text-sm font-medium text-left">
            Shops
          </button>
          <button className="text-white/70 hover:text-white transition-colors py-2 text-sm font-medium text-left">
            Informations
          </button>
          <button className="text-white/70 hover:text-white transition-colors py-2 text-sm font-medium text-left">
            Embeds
          </button>
          <Link href="https://discord.gg/heistbot" className="bg-[#5865f2] hover:bg-[#4752c4] text-white px-4 py-2 rounded-lg font-medium transition-colors mt-2 text-center">
            Login with Discord
          </Link>
        </div>
      </div>
    </nav>
  )
}

