'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface Stats {
  guilds: number;
  users: number;
  shards: number;
  ping: number;
  commands: number;
  uptime: number;
}

export default function Home() {
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        const response = await fetch("https://api.stun.lat/stats");
        if (!response.ok) throw new Error("Failed to fetch stats");
        const data = await response.json();
        setStats(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch data");
        // Fallback data
        setStats({
          guilds: 100,
          users: 1000,
          shards: 2,
          ping: 150,
          commands: 112,
          uptime: 99.9
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  return (
    <main className="min-h-screen w-full bg-[#0F0F0F]">
      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-6 pt-24 pb-16">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h1 className="text-6xl lg:text-7xl font-bold text-white">
                stun
              </h1>
              <p className="text-xl text-white/80 leading-relaxed">
                The professional Discord bot for{" "}
                <span className="text-indigo-400">effective moderation</span>{" "}
                and{" "}
                <span className="text-indigo-400">server management</span>.
              </p>
              <p className="text-white/60 text-lg">
                Trusted by{" "}
                <span className="font-semibold text-indigo-400">
                  {loading ? "..." : stats?.users.toLocaleString()}
                </span>{" "}
                users across{" "}
                <span className="font-semibold text-indigo-400">
                  {loading ? "..." : stats?.guilds.toLocaleString()}
                </span>{" "}
                servers with{" "}
                <span className="font-semibold text-indigo-400">
                  {loading ? "..." : stats?.commands}
                </span>{" "}
                commands.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="#"
                className="group bg-indigo-600 hover:bg-indigo-700 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03z"/>
                </svg>
                Add to Discord
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="group-hover:translate-x-0.5 transition-transform">
                  <path stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" d="M5 12h14m-7-7 7 7-7 7"/>
                </svg>
              </Link>
              <Link
                href="/commands"
                className="bg-white/5 hover:bg-white/10 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-200"
              >
                View Commands
              </Link>
            </div>
          </div>

          {/* Right Content - Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6">
              <div className="text-3xl font-bold text-white mb-2">
                {loading ? "..." : stats?.guilds.toLocaleString()}
              </div>
              <div className="text-white/60">Total Servers</div>
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6">
              <div className="text-3xl font-bold text-white mb-2">
                {loading ? "..." : stats?.users.toLocaleString()}
              </div>
              <div className="text-white/60">Total Users</div>
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6">
              <div className="text-3xl font-bold text-white mb-2">
                {loading ? "..." : `${stats?.ping}ms`}
              </div>
              <div className="text-white/60">Latency</div>
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6">
              <div className="text-3xl font-bold text-white mb-2">
                {loading ? "..." : `${stats?.uptime}%`}
              </div>
              <div className="text-white/60">Uptime</div>
            </div>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="mt-8">
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 max-w-md mx-auto">
              <p className="text-red-400 text-sm text-center">
                {error}
              </p>
            </div>
          </div>
        )}
      </div>
    </main>
  );
}
