"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";

interface Stats {
  users: number;
  guilds: number;
  commands: number;
}

export default function Home() {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<Stats | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("https://api.stun.lat/stats");
        if (!response.ok) throw new Error("Failed to fetch stats");
        const data = await response.json();
        setStats(data);
      } catch (err) {
        console.error("Error fetching stats:", err);
        // Fallback data
        setStats({
          users: 64941,
          guilds: 9,
          commands: 112
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="min-h-screen w-full bg-[#0B0B0F] text-white">
      <div className="max-w-6xl mx-auto px-6 py-24 flex flex-col items-center">
        {/* Hero Section */}
        <h1 className="text-7xl sm:text-8xl font-bold mb-4">stun</h1>
        <p className="text-xl text-gray-300 text-center max-w-2xl mb-2">
          The professional Discord bot for{" "}
          <span className="text-indigo-400">effective moderation</span>
          {" "}and{" "}
          <span className="text-indigo-400">server management</span>
        </p>
        <p className="text-gray-400 text-center mb-8">
          Trusted by{" "}
          <span className="text-indigo-400 font-medium">{loading ? "..." : stats?.users.toLocaleString()}</span>
          {" "}users across{" "}
          <span className="text-indigo-400 font-medium">{loading ? "..." : stats?.guilds}</span>
          {" "}servers with{" "}
          <span className="text-indigo-400 font-medium">{loading ? "..." : stats?.commands}</span>
          {" "}commands.
        </p>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 mb-16">
          <Button
            size="lg"
            className="bg-indigo-500 hover:bg-indigo-600 text-white px-8"
            onClick={() => window.open("https://discord.com/api/oauth2/authorize?client_id=1140629043388829726&permissions=8&scope=bot%20applications.commands")}
          >
            Add to Discord
          </Button>
          <Button
            size="lg"
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-gray-800"
            onClick={() => window.location.href = "/commands"}
          >
            View Commands
          </Button>
        </div>
      </div>
    </div>
  );
}

  return (
    <div className="min-h-screen w-full bg-[#0B0B0F] text-white">
      <div className="max-w-6xl mx-auto px-6 py-24 flex flex-col items-center">
        {/* Hero Section */}
        <h1 className="text-7xl sm:text-8xl font-bold mb-4">stun</h1>
        <p className="text-xl text-gray-300 text-center max-w-2xl mb-2">
          The professional Discord bot for{" "}
          <span className="text-indigo-400">effective moderation</span>
          {" "}and{" "}
          <span className="text-indigo-400">server management</span>
        </p>
        <p className="text-gray-400 text-center mb-8">
          Trusted by{" "}
          <span className="text-indigo-400 font-medium">{loading ? "..." : stats?.users.toLocaleString()}</span>
          {" "}users across{" "}
          <span className="text-indigo-400 font-medium">{loading ? "..." : stats?.guilds}</span>
          {" "}servers with{" "}
          <span className="text-indigo-400 font-medium">{loading ? "..." : stats?.commands}</span>
          {" "}commands.
        </p>

      {/* Main Content */}
      <div className="relative z-10 pt-24 pb-16">
        <div className="max-w-7xl mx-auto px-6">
          {/* Hero Section - More Unique Layout */}
          <div className="grid lg:grid-cols-12 gap-8 items-center mb-24">
            {/* Left Content - Takes more space */}
            <div className="lg:col-span-7 space-y-8">
              <div className="space-y-6">
                <h1 className="text-7xl lg:text-8xl font-black text-white leading-none">
                  stun
                </h1>
                <div className="space-y-4">
                  <p className="text-xl text-white/90 leading-relaxed max-w-2xl">
                    The professional Discord bot for{" "}
                    <span className="font-bold bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] bg-clip-text text-transparent">
                      effective moderation
                    </span>{" "}
                    and{" "}
                    <span className="font-bold bg-gradient-to-r from-[#9b99b5] to-[#aab2d0] bg-clip-text text-transparent">
                      server management
                    </span>.
                  </p>
                  <p className="text-white/70 text-lg max-w-xl">
                    Trusted by{" "}
                    <span className="font-bold text-[#aab2d0]">
                      {loading ? "..." : totalMembers.toLocaleString()}
                    </span>{" "}
                    users across{" "}
                    <span className="font-bold text-[#aab2d0]">
                      {loading ? "..." : totalServers}
                    </span>{" "}
                    servers with{" "}
                    <span className="font-bold text-[#aab2d0]">112</span>{" "}
                    commands.
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="#"
                  className="group relative bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] hover:from-[#9b99b5] hover:to-[#aab2d0] text-white font-bold px-8 py-4 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center justify-center gap-3"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                  </svg>
                  Add to Discord
                  <svg width="18" height="18" fill="none" viewBox="0 0 24 24" className="group-hover:translate-x-1 transition-transform">
                    <path fill="currentColor" d="M14.7 5.3a1 1 0 0 1 1.4 0l5 5a1 1 0 0 1 0 1.4l-5 5a1 1 0 1 1-1.4-1.4l2.3-2.3H5a1 1 0 1 1 0-2h12.01l-2.3-2.3a1 1 0 0 1 0-1.4Z"/>
                  </svg>
                </Link>
                <Link
                  href="/commands"
                  className="bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 text-white font-bold px-8 py-4 rounded-xl transition-all duration-300 hover:scale-105"
                >
                  View Commands
                </Link>
              </div>
            </div>

            {/* Right Avatar - Unique Design */}
            <div className="lg:col-span-5 flex justify-center lg:justify-end">
              <div className="relative">
                {/* Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-[#aab2d0]/30 to-[#9b99b5]/30 rounded-3xl blur-3xl scale-110 animate-pulse" />

                {/* Main Avatar Container */}
                <div className="relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
                  <div className="relative">
                    <div className="w-72 h-72 lg:w-80 lg:h-80 rounded-2xl overflow-hidden bg-gradient-to-br from-[#aab2d0]/20 to-[#9b99b5]/20 border border-white/30">
                      <Image
                        src="/window.svg"
                        alt="stun avatar"
                        width={320}
                        height={320}
                        className="w-full h-full object-cover filter drop-shadow-2xl"
                      />
                    </div>

                    {/* Floating Elements */}
                    <div className="absolute -top-4 -right-4 bg-green-400 text-black px-3 py-1 rounded-full text-sm font-bold shadow-lg">
                      Online
                    </div>
                    <div className="absolute -bottom-4 -left-4 bg-[#aab2d0] text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                      {loading ? "..." : totalShards} Shards
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Section - Unique Grid Layout */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 max-w-6xl mx-auto">
            {/* Primary Stats */}
            <div className="bg-gradient-to-br from-[#aab2d0]/20 to-[#9b99b5]/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:scale-105 transition-transform duration-300">
              <div className="text-4xl font-black text-[#aab2d0] mb-2">
                {loading ? "..." : totalMembers.toLocaleString()}
              </div>
              <div className="text-white/70 text-sm font-medium uppercase tracking-wider">
                Daily Users
              </div>
            </div>

            <div className="bg-gradient-to-br from-[#9b99b5]/20 to-[#aab2d0]/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:scale-105 transition-transform duration-300">
              <div className="text-4xl font-black text-[#9b99b5] mb-2">
                {loading ? "..." : totalServers}
              </div>
              <div className="text-white/70 text-sm font-medium uppercase tracking-wider">
                Guilds
              </div>
            </div>

            <div className="bg-gradient-to-br from-[#aab2d0]/20 to-[#9b99b5]/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:scale-105 transition-transform duration-300">
              <div className="text-4xl font-black text-[#aab2d0] mb-2">
                {loading ? "..." : totalShards}
              </div>
              <div className="text-white/70 text-sm font-medium uppercase tracking-wider">
                Shards
              </div>
            </div>

            <div className="bg-gradient-to-br from-[#9b99b5]/20 to-[#aab2d0]/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:scale-105 transition-transform duration-300">
              <div className="text-4xl font-black text-[#9b99b5] mb-2">
                {loading ? "..." : `${avgLatency}ms`}
              </div>
              <div className="text-white/70 text-sm font-medium uppercase tracking-wider">
                Ping
              </div>
            </div>

            <div className="bg-gradient-to-br from-[#aab2d0]/20 to-[#9b99b5]/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:scale-105 transition-transform duration-300">
              <div className="text-4xl font-black text-[#aab2d0] mb-2">
                112
              </div>
              <div className="text-white/70 text-sm font-medium uppercase tracking-wider">
                Commands
              </div>
            </div>
          </div>

          {/* Additional Technical Stats */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 max-w-6xl mx-auto">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center hover:bg-white/10 transition-colors">
              <div className="text-2xl font-bold text-white mb-1">
                {additionalStats.codeLines.toLocaleString()}
              </div>
              <div className="text-white/60 text-xs uppercase tracking-wide">
                Code Lines
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center hover:bg-white/10 transition-colors">
              <div className="text-2xl font-bold text-white mb-1">
                {additionalStats.functions.toLocaleString()}
              </div>
              <div className="text-white/60 text-xs uppercase tracking-wide">
                Functions
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center hover:bg-white/10 transition-colors">
              <div className="text-2xl font-bold text-white mb-1">
                {additionalStats.classes}
              </div>
              <div className="text-white/60 text-xs uppercase tracking-wide">
                Classes
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center hover:bg-white/10 transition-colors">
              <div className="text-2xl font-bold text-white mb-1">
                {additionalStats.dbRows.toLocaleString()}
              </div>
              <div className="text-white/60 text-xs uppercase tracking-wide">
                DB Rows
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center hover:bg-white/10 transition-colors">
              <div className="text-2xl font-bold text-white mb-1">
                {additionalStats.dbSize}
              </div>
              <div className="text-white/60 text-xs uppercase tracking-wide">
                DB Size
              </div>
            </div>
          </div>

          {/* Status Indicators */}
          {!loading && shardData.length > 0 && (
            <div className="mt-16 flex flex-wrap justify-center gap-4">
              {shardData.map((shard) => (
                <div
                  key={shard.shard_id}
                  className="flex items-center gap-2 bg-white/5 backdrop-blur-sm border border-white/10 rounded-full px-4 py-2 text-sm"
                >
                  <div className={`w-2 h-2 rounded-full ${
                    shard.is_ready ? 'bg-green-400' : 'bg-red-400'
                  } animate-pulse`} />
                  <span className="text-white/70">
                    shard {shard.shard_id} - {shard.server_count} guilds
                  </span>
                </div>
              ))}
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="mt-8 text-center">
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 max-w-md mx-auto">
                <p className="text-red-400 text-sm">
                  {error}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

